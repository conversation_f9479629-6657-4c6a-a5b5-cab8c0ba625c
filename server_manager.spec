# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# تحديد المسار الأساسي للمشروع
project_root = Path(__file__).parent

# قائمة الملفات والمجلدات المطلوبة
datas = [
    (str(project_root / 'app'), 'app'),
    (str(project_root / 'schema.sql'), '.'),
    (str(project_root / 'config.py'), '.'),
    (str(project_root / 'file.jpeg'), '.'),
    (str(project_root / 'file.ico'), '.'),
    (str(project_root / 'monkey_patch.py'), '.'),
    (str(project_root / 'fix_paths.py'), '.'),
    (str(project_root / 'path_manager.py'), '.'),
    (str(project_root / 'exe_config.py'), '.'),
]

# إضافة مجلد templates إذا كان موجوداً
templates_path = project_root / 'app' / 'templates'
if templates_path.exists():
    datas.append((str(templates_path), 'app/templates'))

# إضافة مجلد static إذا كان موجوداً
static_path = project_root / 'app' / 'static'
if static_path.exists():
    datas.append((str(static_path), 'app/static'))

# قائمة الوحدات المخفية المطلوبة
hiddenimports = [
    # Flask والمكتبات المرتبطة
    'flask',
    'flask_login',
    'flask_wtf',
    'flask_bcrypt',
    'flask_mail',
    'werkzeug',
    'werkzeug.urls',
    'werkzeug.serving',
    'werkzeug.datastructures',
    'jinja2',
    'itsdangerous',
    'email_validator',
    
    # PyQt5
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    
    # مكتبات أخرى
    'PIL',
    'PIL.Image',
    'qrcode',
    'qrcode.image',
    'qrcode.image.pil',
    'bcrypt',
    'sqlite3',
    'urllib.parse',
    'importlib.util',
    
    # وحدات التطبيق
    'app',
    'app.auth',
    'app.auth.routes',
    'app.dashboard',
    'app.dashboard.routes',
    'app.owners',
    'app.owners.routes',
    'app.properties',
    'app.properties.routes',
    'app.tenants',
    'app.tenants.routes',
    'app.documents',
    'app.documents.routes',
    'app.finance',
    'app.finance.routes',
    'app.reports',
    'app.reports.routes',
    'app.db',
    'app.models',
    'app.forms',
    'app.utils',
    'app.decorators',
    'app.db_manager',
    
    # وحدات النظام
    'threading',
    'socket',
    'webbrowser',
    'subprocess',
    'signal',
    'logging',
    'traceback',
    'datetime',
    'io',
    'pathlib',
    
    # وحدات مخصصة
    'path_manager',
    'exe_config',
    'monkey_patch',
    'fix_paths',
]

# استبعاد وحدات غير ضرورية لتقليل حجم الملف
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pdb',
    'pydoc',
    'xml.etree.ElementTree',
]

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# إزالة ملفات غير ضرورية
a.datas = [x for x in a.datas if not x[0].startswith('tcl')]
a.datas = [x for x in a.datas if not x[0].startswith('tk')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='مدير_خادم_مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'file.ico') if (project_root / 'file.ico').exists() else None,
    version_file=None,
)
