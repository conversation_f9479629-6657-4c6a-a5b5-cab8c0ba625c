#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت إعداد البيئة وتثبيت المكتبات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """تشغيل أمر مع وصف"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"   ✅ تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ فشل: {e}")
        if e.stderr:
            print(f"   تفاصيل: {e.stderr}")
        return False

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    version = sys.version_info
    print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ⚠️  تحذير: يُنصح باستخدام Python 3.8 أو أحدث")
        return False
    else:
        print("   ✅ إصدار Python مناسب")
        return True

def upgrade_pip():
    """ترقية pip"""
    return run_command(f"{sys.executable} -m pip install --upgrade pip", "ترقية pip")

def install_requirements():
    """تثبيت المكتبات من requirements.txt"""
    if os.path.exists("requirements.txt"):
        return run_command(f"{sys.executable} -m pip install -r requirements.txt", "تثبيت المكتبات الأساسية")
    else:
        print("   ⚠️  ملف requirements.txt غير موجود")
        return False

def install_build_requirements():
    """تثبيت مكتبات البناء"""
    if os.path.exists("requirements_build.txt"):
        return run_command(f"{sys.executable} -m pip install -r requirements_build.txt", "تثبيت مكتبات البناء")
    else:
        print("   ⚠️  ملف requirements_build.txt غير موجود")
        return False

def install_pyinstaller():
    """تثبيت PyInstaller"""
    return run_command(f"{sys.executable} -m pip install pyinstaller", "تثبيت PyInstaller")

def test_imports():
    """اختبار استيراد المكتبات الأساسية"""
    print("🧪 اختبار استيراد المكتبات...")
    
    libraries = [
        ("flask", "Flask"),
        ("PyQt5.QtWidgets", "PyQt5"),
        ("qrcode", "QR Code"),
        ("bcrypt", "bcrypt"),
        ("PIL", "Pillow")
    ]
    
    all_success = True
    for lib, name in libraries:
        try:
            __import__(lib)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - غير مثبت")
            all_success = False
    
    return all_success

def create_directories():
    """إنشاء المجلدات الضرورية"""
    print("📁 إنشاء المجلدات الضرورية...")
    
    directories = [
        "instance",
        "uploads",
        "uploads/buildings",
        "uploads/contracts", 
        "uploads/documents",
        "uploads/owners",
        "uploads/tenants",
        "uploads/transactions"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ {directory}")
        except Exception as e:
            print(f"   ❌ فشل في إنشاء {directory}: {e}")
            return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد بيئة مشروع مكتب عصام الفت")
    print("=" * 50)
    
    # التحقق من Python
    if not check_python_version():
        print("⚠️  قد تواجه مشاكل مع إصدار Python الحالي")
    
    # ترقية pip
    upgrade_pip()
    
    # تثبيت المكتبات الأساسية
    if not install_requirements():
        print("❌ فشل في تثبيت المكتبات الأساسية")
        return False
    
    # تثبيت مكتبات البناء
    if not install_build_requirements():
        print("⚠️  فشل في تثبيت مكتبات البناء، سيتم تثبيت PyInstaller فقط")
        install_pyinstaller()
    
    # إنشاء المجلدات
    create_directories()
    
    # اختبار الاستيراد
    if test_imports():
        print("✅ جميع المكتبات مثبتة بنجاح")
    else:
        print("⚠️  بعض المكتبات غير مثبتة بشكل صحيح")
    
    print("=" * 50)
    print("🎉 تم إعداد البيئة!")
    print("💡 يمكنك الآن تشغيل build.py أو build_simple.py لبناء الملف التنفيذي")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
