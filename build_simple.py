#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت بناء مبسط لتحويل المشروع إلى ملف EXE

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import subprocess
import sys

def run_command(cmd):
    """تشغيل أمر وطباعة النتيجة"""
    print(f"🔧 تشغيل: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False

def main():
    print("🚀 بناء مشروع مكتب عصام الفت")
    print("=" * 40)
    
    # 1. تثبيت PyInstaller
    print("📦 تثبيت PyInstaller...")
    if not run_command("pip install pyinstaller"):
        return False
    
    # 2. تنظيف ملفات البناء السابقة
    print("🧹 تنظيف ملفات البناء السابقة...")
    run_command("rmdir /s /q dist")
    run_command("rmdir /s /q build")
    run_command("del server_manager.spec")
    
    # 3. بناء الملف التنفيذي
    print("🔨 بناء الملف التنفيذي...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--clean",
        "--noconfirm",
        "--name=مدير_خادم_مكتب_عصام_الفت",
        "--icon=file.ico",
        "--add-data=app;app",
        "--add-data=schema.sql;.",
        "--add-data=config.py;.",
        "--add-data=file.jpeg;.",
        "--add-data=monkey_patch.py;.",
        "--add-data=fix_paths.py;.",
        "--hidden-import=flask",
        "--hidden-import=flask_login",
        "--hidden-import=flask_wtf",
        "--hidden-import=flask_bcrypt",
        "--hidden-import=PyQt5",
        "--hidden-import=qrcode",
        "--hidden-import=bcrypt",
        "--hidden-import=sqlite3",
        "--hidden-import=app",
        "server_manager.py"
    ]
    
    if not run_command(" ".join(cmd)):
        return False
    
    print("✅ تم البناء بنجاح!")
    print("📁 الملف التنفيذي موجود في مجلد: dist/")
    
    return True

if __name__ == "__main__":
    if main():
        print("\n🎉 تم إنجاز البناء بنجاح!")
    else:
        print("\n❌ فشل في البناء!")
    
    input("اضغط Enter للخروج...")
