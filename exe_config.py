#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تكوين خاص للملف التنفيذي - يتم تشغيله قبل أي شيء آخر

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import logging

# إعداد التسجيل المبكر
def setup_early_logging():
    """إعداد نظام التسجيل المبكر"""
    try:
        # تحديد مسار ملف السجل
        if getattr(sys, 'frozen', False):
            log_dir = os.path.dirname(sys.executable)
        else:
            log_dir = os.path.dirname(os.path.abspath(__file__))
        
        log_file = os.path.join(log_dir, 'exe_debug.log')
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger('exe_config')
        logger.info("تم إعداد نظام التسجيل")
        return logger
    except Exception as e:
        print(f"فشل في إعداد التسجيل: {e}")
        return None

# إعداد الترميز
def setup_encoding():
    """إعداد ترميز النصوص"""
    try:
        # ضبط ترميز النظام
        if sys.platform.startswith('win'):
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            
            if hasattr(sys, 'frozen'):
                # في حالة الملف التنفيذي
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except AttributeError:
                    # للإصدارات الأقدم من Python
                    import codecs
                    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
                    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
        
        print("تم إعداد الترميز بنجاح")
        return True
    except Exception as e:
        print(f"فشل في إعداد الترميز: {e}")
        return False

# إعداد المسارات
def setup_paths():
    """إعداد مسارات النظام"""
    try:
        # تحديد المسار الأساسي
        if getattr(sys, 'frozen', False):
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller onefile
                temp_path = sys._MEIPASS
                app_path = os.path.dirname(sys.executable)
            else:
                # PyInstaller onedir
                app_path = os.path.dirname(sys.executable)
                temp_path = app_path
        else:
            # تشغيل كسكريبت
            app_path = os.path.dirname(os.path.abspath(__file__))
            temp_path = app_path
        
        # تغيير مجلد العمل
        os.chdir(app_path)
        
        # إضافة المسارات إلى sys.path
        paths_to_add = [app_path, temp_path]
        
        # إضافة مجلد _internal إذا كان موجوداً
        internal_path = os.path.join(app_path, '_internal')
        if os.path.exists(internal_path):
            paths_to_add.append(internal_path)
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        print(f"تم إعداد المسارات:")
        print(f"  - مسار التطبيق: {app_path}")
        print(f"  - مسار مؤقت: {temp_path}")
        
        return app_path, temp_path
    except Exception as e:
        print(f"فشل في إعداد المسارات: {e}")
        return None, None

# إنشاء المجلدات الضرورية
def create_directories(app_path):
    """إنشاء المجلدات الضرورية"""
    try:
        required_dirs = [
            'instance',
            'uploads',
            'uploads/buildings',
            'uploads/contracts',
            'uploads/documents',
            'uploads/owners', 
            'uploads/tenants',
            'uploads/transactions'
        ]
        
        for dir_name in required_dirs:
            dir_path = os.path.join(app_path, dir_name)
            os.makedirs(dir_path, exist_ok=True)
        
        print("تم إنشاء المجلدات الضرورية")
        return True
    except Exception as e:
        print(f"فشل في إنشاء المجلدات: {e}")
        return False

# تطبيق إصلاحات Werkzeug
def apply_werkzeug_patches():
    """تطبيق إصلاحات Werkzeug"""
    try:
        print("تطبيق إصلاحات Werkzeug...")
        
        import werkzeug.urls
        from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus
        
        # إضافة url_decode إذا لم تكن موجودة
        if not hasattr(werkzeug.urls, 'url_decode'):
            def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                """تنفيذ بديل لـ url_decode"""
                try:
                    from werkzeug.datastructures import MultiDict
                    result_cls = MultiDict
                except ImportError:
                    result_cls = dict
                
                if cls is not None:
                    result_cls = cls
                
                result = result_cls()
                parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
                
                for key, values in parsed.items():
                    for value in values:
                        if hasattr(result, 'add'):
                            result.add(key, value)
                        else:
                            result[key] = value
                
                return result
            
            werkzeug.urls.url_decode = url_decode
            print("  ✅ تم إضافة url_decode")
        
        # إضافة الدوال المفقودة الأخرى
        functions_to_add = {
            'url_quote': quote,
            'url_quote_plus': quote_plus,
            'url_unquote': unquote,
            'url_unquote_plus': unquote_plus
        }
        
        for func_name, func in functions_to_add.items():
            if not hasattr(werkzeug.urls, func_name):
                setattr(werkzeug.urls, func_name, func)
                print(f"  ✅ تم إضافة {func_name}")
        
        print("تم تطبيق إصلاحات Werkzeug بنجاح")
        return True
    except Exception as e:
        print(f"فشل في تطبيق إصلاحات Werkzeug: {e}")
        return False

# الدالة الرئيسية للتكوين
def configure_exe_environment():
    """تكوين بيئة الملف التنفيذي"""
    print("🔧 تكوين بيئة الملف التنفيذي...")
    print("=" * 40)
    
    # إعداد التسجيل
    logger = setup_early_logging()
    
    # إعداد الترميز
    setup_encoding()
    
    # إعداد المسارات
    app_path, temp_path = setup_paths()
    if not app_path:
        print("❌ فشل في إعداد المسارات")
        return False
    
    # إنشاء المجلدات
    create_directories(app_path)
    
    # تطبيق إصلاحات Werkzeug
    apply_werkzeug_patches()
    
    print("=" * 40)
    print("✅ تم تكوين بيئة الملف التنفيذي بنجاح")
    
    return True

# تطبيق التكوين تلقائياً عند الاستيراد
if __name__ != "__main__":
    configure_exe_environment()
