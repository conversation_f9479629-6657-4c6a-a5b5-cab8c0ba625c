#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
مدير المسارات للتطبيق - يعمل مع الملف التنفيذي والسكريبت

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
from pathlib import Path

class PathManager:
    """مدير المسارات للتطبيق"""
    
    def __init__(self):
        self._application_path = None
        self._is_frozen = getattr(sys, 'frozen', False)
        self._setup_paths()
    
    def _setup_paths(self):
        """إعداد المسارات الأساسية"""
        if self._is_frozen:
            # في حالة التشغيل كملف تنفيذي
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller onefile mode
                self._temp_path = Path(sys._MEIPASS)
                self._application_path = Path(sys.executable).parent
            else:
                # PyInstaller onedir mode
                self._application_path = Path(sys.executable).parent
                self._temp_path = self._application_path
        else:
            # في حالة التشغيل كسكريبت
            self._application_path = Path(__file__).parent
            self._temp_path = self._application_path
        
        # إضافة المسارات إلى sys.path
        self._add_to_path()
        
        # إنشاء المجلدات الضرورية
        self._create_required_directories()
    
    def _add_to_path(self):
        """إضافة المسارات إلى sys.path"""
        paths_to_add = [
            str(self._application_path),
            str(self._temp_path),
        ]
        
        # إضافة مجلد _internal إذا كان موجوداً
        internal_path = self._application_path / '_internal'
        if internal_path.exists():
            paths_to_add.append(str(internal_path))
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
    
    def _create_required_directories(self):
        """إنشاء المجلدات الضرورية"""
        required_dirs = [
            'instance',
            'uploads',
            'uploads/buildings',
            'uploads/contracts',
            'uploads/documents', 
            'uploads/owners',
            'uploads/tenants',
            'uploads/transactions'
        ]
        
        for dir_name in required_dirs:
            dir_path = self._application_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
    
    @property
    def application_path(self):
        """المسار الأساسي للتطبيق"""
        return self._application_path
    
    @property
    def temp_path(self):
        """المسار المؤقت (للملفات المدمجة)"""
        return self._temp_path
    
    @property
    def is_frozen(self):
        """هل التطبيق يعمل كملف تنفيذي"""
        return self._is_frozen
    
    def get_resource_path(self, relative_path):
        """الحصول على مسار مورد (ملف مدمج أو خارجي)"""
        # البحث في المسار المؤقت أولاً (للملفات المدمجة)
        temp_file = self._temp_path / relative_path
        if temp_file.exists():
            return temp_file
        
        # البحث في مسار التطبيق
        app_file = self._application_path / relative_path
        if app_file.exists():
            return app_file
        
        # إرجاع المسار النسبي كما هو
        return Path(relative_path)
    
    def get_data_path(self, relative_path):
        """الحصول على مسار ملف بيانات (دائماً في مسار التطبيق)"""
        return self._application_path / relative_path
    
    def get_config_path(self):
        """الحصول على مسار ملف التكوين"""
        return self.get_resource_path('config.py')
    
    def get_schema_path(self):
        """الحصول على مسار ملف قاعدة البيانات"""
        return self.get_resource_path('schema.sql')
    
    def get_database_path(self):
        """الحصول على مسار قاعدة البيانات"""
        return self.get_data_path('instance/realestate.db')
    
    def get_uploads_path(self):
        """الحصول على مسار مجلد الرفع"""
        return self.get_data_path('uploads')
    
    def get_icon_path(self):
        """الحصول على مسار أيقونة التطبيق"""
        # البحث عن الأيقونة بصيغ مختلفة
        for ext in ['.ico', '.png', '.jpg', '.jpeg']:
            icon_path = self.get_resource_path(f'file{ext}')
            if icon_path.exists():
                return icon_path
        return None

# إنشاء مثيل عام لمدير المسارات
path_manager = PathManager()

def get_application_path():
    """الحصول على المسار الأساسي للتطبيق"""
    return path_manager.application_path

def get_resource_path(relative_path):
    """الحصول على مسار مورد"""
    return path_manager.get_resource_path(relative_path)

def get_data_path(relative_path):
    """الحصول على مسار ملف بيانات"""
    return path_manager.get_data_path(relative_path)

def is_frozen():
    """هل التطبيق يعمل كملف تنفيذي"""
    return path_manager.is_frozen

# تطبيق إعدادات المسارات عند الاستيراد
def apply_path_fixes():
    """تطبيق إصلاحات المسارات"""
    # تغيير مجلد العمل الحالي
    os.chdir(str(path_manager.application_path))
    
    # ضبط متغيرات البيئة
    os.environ['PYTHONPATH'] = str(path_manager.application_path)
    
    print(f"تم تطبيق إعدادات المسارات:")
    print(f"  - مسار التطبيق: {path_manager.application_path}")
    print(f"  - مسار مؤقت: {path_manager.temp_path}")
    print(f"  - ملف تنفيذي: {path_manager.is_frozen}")

# تطبيق الإعدادات تلقائياً عند الاستيراد
apply_path_fixes()
