# دليل بناء مشروع مكتب عصام الفت

## نظرة عامة

هذا الدليل يشرح كيفية تحويل مشروع Flask مع GUI إلى ملف EXE يعمل على جميع أنظمة Windows بدون الحاجة لتثبيت Python أو أي مكتبة خارجية.

## المتطلبات

- Python 3.8 أو أحدث
- نظام Windows (مُختبر على Windows 10/11)
- مساحة قرص صلب: 500 ميجابايت على الأقل

## طرق البناء

### الطريقة الأولى: البناء التلقائي (الأسهل)

1. **تشغيل ملف البناء التلقائي:**
   ```bash
   run_build.bat
   ```

2. **انتظار انتهاء العملية** (قد تستغرق 5-10 دقائق)

3. **العثور على الملف التنفيذي** في مجلد `dist/`

### الطريقة الثانية: البناء اليدوي

1. **إعداد البيئة:**
   ```bash
   python setup_environment.py
   ```

2. **البناء الكامل:**
   ```bash
   python build.py
   ```

3. **أو البناء المبسط:**
   ```bash
   python build_simple.py
   ```

### الطريقة الثالثة: البناء باستخدام PyInstaller مباشرة

```bash
pip install pyinstaller
pyinstaller --onefile --windowed --clean --noconfirm --name="مدير_خادم_مكتب_عصام_الفت" --icon=file.ico --add-data="app;app" --add-data="schema.sql;." --add-data="config.py;." --add-data="file.jpeg;." --hidden-import=flask --hidden-import=PyQt5 --hidden-import=qrcode server_manager.py
```

## بنية المشروع بعد البناء

```
مكتب_عصام_الفت_محمول/
├── مدير_خادم_مكتب_عصام_الفت.exe    # الملف التنفيذي الرئيسي
├── instance/                          # مجلد قاعدة البيانات
├── uploads/                           # مجلد الملفات المرفوعة
│   ├── buildings/
│   ├── contracts/
│   ├── documents/
│   ├── owners/
│   ├── tenants/
│   └── transactions/
└── اقرأني.txt                        # تعليمات الاستخدام
```

## الميزات

### ✅ المزايا
- **ملف واحد**: جميع المكتبات مدمجة
- **محمول**: لا يحتاج تثبيت
- **آمن**: متوافق مع Windows Defender
- **سهل التوزيع**: نسخ ولصق فقط
- **واجهة عربية**: دعم كامل للغة العربية

### 🔧 الحلول المطبقة
- **إصلاح مشاكل Werkzeug**: تم حل مشاكل التوافق
- **إدارة المسارات**: نظام ذكي للمسارات
- **دعم قاعدة البيانات**: SQLite مدمجة
- **معالجة الأخطاء**: نظام شامل لمعالجة الأخطاء

## استكشاف الأخطاء

### مشكلة: فشل في تثبيت المكتبات
**الحل:**
```bash
pip install --upgrade pip
pip install -r requirements_build.txt
```

### مشكلة: خطأ في PyInstaller
**الحل:**
```bash
pip uninstall pyinstaller
pip install pyinstaller==6.3.0
```

### مشكلة: Windows Defender يحذف الملف
**الحل:**
1. إضافة استثناء في Windows Defender
2. استخدام البناء المبسط: `python build_simple.py`

### مشكلة: الملف التنفيذي لا يعمل
**الحل:**
1. التأكد من وجود جميع الملفات المطلوبة
2. تشغيل الملف من Command Prompt لرؤية الأخطاء
3. التحقق من ملف `exe_debug.log`

## الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `server_manager.py` | الملف الرئيسي للتطبيق |
| `build.py` | سكريبت البناء الكامل |
| `build_simple.py` | سكريبت البناء المبسط |
| `setup_environment.py` | إعداد البيئة |
| `path_manager.py` | إدارة المسارات |
| `exe_config.py` | تكوين الملف التنفيذي |
| `requirements_build.txt` | مكتبات البناء |

## معلومات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## الدعم الفني

- **فيسبوك:** https://www.facebook.com/almbarmg
- **واتساب:** 0201032540807

## حقوق الملكية

تم التطوير والبرمجة بواسطة شركة المبرمج المصري  
جميع الحقوق محفوظة © 2025

## ملاحظات مهمة

1. **حجم الملف:** الملف التنفيذي قد يكون كبير الحجم (100-200 ميجابايت) بسبب دمج جميع المكتبات
2. **الأداء:** أول تشغيل قد يكون بطيء قليلاً
3. **التوافق:** مُختبر على Windows 10/11، قد يعمل على Windows 7/8
4. **الأمان:** آمن تماماً ولا يحتوي على أي برمجيات ضارة

## خطوات ما بعد البناء

1. **اختبار الملف التنفيذي** على نفس الجهاز
2. **اختبار على جهاز آخر** بدون Python
3. **إنشاء نسخة احتياطية** من المجلد الكامل
4. **توزيع المجلد** كاملاً (ليس الملف فقط)

---

**نصيحة:** احتفظ بنسخة من الكود المصدري دائماً للتطوير المستقبلي!
