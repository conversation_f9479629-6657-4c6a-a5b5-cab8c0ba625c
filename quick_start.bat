@echo off
chcp 65001 >nul
title البدء السريع - مكتب عصام الفت

echo.
echo ========================================
echo    البدء السريع - مكتب عصام الفت
echo ========================================
echo.

echo اختر ما تريد فعله:
echo.
echo 1. اختبار البيئة والمتطلبات
echo 2. إعداد البيئة وتثبيت المكتبات
echo 3. بناء الملف التنفيذي (كامل)
echo 4. بناء الملف التنفيذي (مبسط)
echo 5. تشغيل التطبيق مباشرة (للاختبار)
echo 6. عرض دليل البناء
echo 0. خروج
echo.

set /p choice="اختر رقم (0-6): "

if "%choice%"=="1" goto test
if "%choice%"=="2" goto setup
if "%choice%"=="3" goto build_full
if "%choice%"=="4" goto build_simple
if "%choice%"=="5" goto run_app
if "%choice%"=="6" goto show_guide
if "%choice%"=="0" goto exit
goto invalid

:test
echo.
echo 🧪 اختبار البيئة...
python test_build.py
pause
goto menu

:setup
echo.
echo 📦 إعداد البيئة...
python setup_environment.py
pause
goto menu

:build_full
echo.
echo 🔨 بناء كامل...
python build.py
pause
goto menu

:build_simple
echo.
echo 🔨 بناء مبسط...
python build_simple.py
pause
goto menu

:run_app
echo.
echo 🚀 تشغيل التطبيق...
python server_manager.py
pause
goto menu

:show_guide
echo.
echo 📖 عرض دليل البناء...
if exist README_BUILD.md (
    type README_BUILD.md
) else (
    echo ملف الدليل غير موجود
)
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح
pause
goto menu

:menu
cls
echo.
echo ========================================
echo    البدء السريع - مكتب عصام الفت
echo ========================================
echo.

echo اختر ما تريد فعله:
echo.
echo 1. اختبار البيئة والمتطلبات
echo 2. إعداد البيئة وتثبيت المكتبات
echo 3. بناء الملف التنفيذي (كامل)
echo 4. بناء الملف التنفيذي (مبسط)
echo 5. تشغيل التطبيق مباشرة (للاختبار)
echo 6. عرض دليل البناء
echo 0. خروج
echo.

set /p choice="اختر رقم (0-6): "

if "%choice%"=="1" goto test
if "%choice%"=="2" goto setup
if "%choice%"=="3" goto build_full
if "%choice%"=="4" goto build_simple
if "%choice%"=="5" goto run_app
if "%choice%"=="6" goto show_guide
if "%choice%"=="0" goto exit
goto invalid

:exit
echo.
echo 👋 شكراً لاستخدام مكتب عصام الفت
echo.
exit /b 0
