#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إنشاء مثبت للتطبيق

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import shutil
import zipfile
from pathlib import Path
from version_info import *

class InstallerCreator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.installer_dir = self.project_root / "installer"
        self.package_name = f"مكتب_عصام_الفت_v{APP_VERSION}"
        
    def create_portable_package(self):
        """إنشاء حزمة محمولة"""
        print("📦 إنشاء الحزمة المحمولة...")
        
        # إنشاء مجلد الحزمة
        package_dir = self.installer_dir / self.package_name
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir(parents=True)
        
        # نسخ الملف التنفيذي
        exe_file = self.dist_dir / EXE_NAME
        if exe_file.exists():
            shutil.copy2(exe_file, package_dir)
            print(f"   ✅ تم نسخ: {EXE_NAME}")
        else:
            print(f"   ❌ الملف التنفيذي غير موجود: {exe_file}")
            return False
        
        # إنشاء المجلدات الضرورية
        essential_dirs = [
            'instance',
            'uploads',
            'uploads/buildings',
            'uploads/contracts',
            'uploads/documents',
            'uploads/owners',
            'uploads/tenants',
            'uploads/transactions'
        ]
        
        for dir_name in essential_dirs:
            (package_dir / dir_name).mkdir(parents=True, exist_ok=True)
        
        # نسخ الملفات الإضافية
        additional_files = [
            'file.jpeg',
            'file.ico'
        ]
        
        for file_name in additional_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                shutil.copy2(src_file, package_dir)
                print(f"   ✅ تم نسخ: {file_name}")
        
        # إنشاء ملف README
        self.create_readme_file(package_dir)
        
        # إنشاء ملف تشغيل سريع
        self.create_run_script(package_dir)
        
        print(f"   ✅ تم إنشاء الحزمة: {package_dir}")
        return package_dir
    
    def create_readme_file(self, package_dir):
        """إنشاء ملف README للحزمة"""
        readme_content = f"""
# {APP_NAME} v{APP_VERSION}

## طريقة التشغيل:
1. قم بتشغيل ملف "{EXE_NAME}"
2. اختر نوع الخادم (محلي أو شبكة)
3. اضغط على "تشغيل الخادم"
4. سيتم فتح المتصفح تلقائياً

## معلومات تسجيل الدخول الافتراضية:
- اسم المستخدم: {DEFAULT_USERNAME}
- كلمة المرور: {DEFAULT_PASSWORD}

## ملاحظات مهمة:
- تأكد من السماح للتطبيق في جدار الحماية
- لا تحذف مجلدات instance أو uploads
- يمكنك نسخ المجلد كاملاً إلى أي مكان آخر
- التطبيق لا يحتاج تثبيت Python أو أي مكتبة خارجية

## المجلدات:
- instance/: قاعدة البيانات
- uploads/: الملفات المرفوعة
  - buildings/: صور المباني
  - contracts/: ملفات العقود
  - documents/: المستندات
  - owners/: ملفات الملاك
  - tenants/: ملفات المستأجرين
  - transactions/: ملفات المعاملات

## استكشاف الأخطاء:
- إذا لم يعمل التطبيق، تأكد من وجود جميع المجلدات
- في حالة مشاكل الشبكة، جرب تشغيل التطبيق كمدير
- للدعم الفني، راجع معلومات الاتصال أدناه

## الدعم الفني:
- فيسبوك: {APP_WEBSITE}
- واتساب: {APP_CONTACT}

{APP_COPYRIGHT}

تاريخ البناء: {BUILD_DATE}
الإصدار: {APP_VERSION}
"""
        
        with open(package_dir / "اقرأني.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("   ✅ تم إنشاء ملف اقرأني.txt")
    
    def create_run_script(self, package_dir):
        """إنشاء سكريبت تشغيل سريع"""
        script_content = f'''@echo off
chcp 65001 >nul
title {APP_NAME}

echo.
echo ========================================
echo    {APP_NAME}
echo    الإصدار: {APP_VERSION}
echo ========================================
echo.

echo 🚀 تشغيل التطبيق...
echo.

if exist "{EXE_NAME}" (
    start "" "{EXE_NAME}"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 💡 إذا لم يفتح المتصفح تلقائياً، افتح:
    echo    http://localhost:{DEFAULT_PORT}
    echo.
    echo 📋 معلومات تسجيل الدخول:
    echo    المستخدم: {DEFAULT_USERNAME}
    echo    كلمة المرور: {DEFAULT_PASSWORD}
) else (
    echo ❌ الملف التنفيذي غير موجود
    echo تأكد من وجود ملف: {EXE_NAME}
)

echo.
pause
'''
        
        with open(package_dir / "تشغيل_سريع.bat", 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("   ✅ تم إنشاء ملف تشغيل_سريع.bat")
    
    def create_zip_archive(self, package_dir):
        """إنشاء أرشيف مضغوط"""
        print("🗜️  إنشاء الأرشيف المضغوط...")
        
        zip_file = self.installer_dir / f"{self.package_name}.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(package_dir.parent)
                    zipf.write(file_path, arc_name)
        
        print(f"   ✅ تم إنشاء الأرشيف: {zip_file}")
        return zip_file
    
    def create_installer(self):
        """إنشاء المثبت الكامل"""
        print("🏗️  إنشاء مثبت التطبيق...")
        print("=" * 50)
        
        # التحقق من وجود الملف التنفيذي
        exe_file = self.dist_dir / EXE_NAME
        if not exe_file.exists():
            print(f"❌ الملف التنفيذي غير موجود: {exe_file}")
            print("يرجى بناء التطبيق أولاً باستخدام build.py")
            return False
        
        # إنشاء مجلد المثبت
        self.installer_dir.mkdir(exist_ok=True)
        
        # إنشاء الحزمة المحمولة
        package_dir = self.create_portable_package()
        if not package_dir:
            return False
        
        # إنشاء الأرشيف المضغوط
        zip_file = self.create_zip_archive(package_dir)
        
        # إنشاء ملف معلومات الإصدار
        self.create_version_file()
        
        print("=" * 50)
        print("🎉 تم إنشاء المثبت بنجاح!")
        print(f"📁 مجلد المثبت: {self.installer_dir}")
        print(f"📦 الحزمة المحمولة: {package_dir}")
        print(f"🗜️  الأرشيف المضغوط: {zip_file}")
        
        return True
    
    def create_version_file(self):
        """إنشاء ملف معلومات الإصدار"""
        version_content = f"""
معلومات الإصدار
================

اسم التطبيق: {APP_NAME}
الإصدار: {APP_VERSION}
الوصف: {APP_DESCRIPTION}

المطور: {APP_AUTHOR}
الموقع: {APP_WEBSITE}
الاتصال: {APP_CONTACT}

تاريخ البناء: {BUILD_DATE}
نوع البناء: {BUILD_TYPE}

{APP_COPYRIGHT}

ملاحظات الإصدار:
- واجهة مستخدم محسنة
- دعم كامل للغة العربية
- نظام إدارة شامل للعقارات
- واجهة ويب متجاوبة
- قاعدة بيانات SQLite مدمجة
- نظام مستخدمين متعدد
- تقارير مفصلة
- نسخ احتياطي تلقائي

متطلبات النظام:
- Windows 7/8/10/11
- ذاكرة: 512 ميجابايت على الأقل
- مساحة القرص: 200 ميجابايت
- لا يتطلب تثبيت Python أو مكتبات إضافية
"""
        
        with open(self.installer_dir / "معلومات_الإصدار.txt", 'w', encoding='utf-8') as f:
            f.write(version_content)
        
        print("   ✅ تم إنشاء ملف معلومات_الإصدار.txt")

def main():
    """الدالة الرئيسية"""
    try:
        creator = InstallerCreator()
        success = creator.create_installer()
        
        if success:
            print("\n✅ تم إنشاء المثبت بنجاح!")
            print("💡 يمكنك الآن توزيع الحزمة أو الأرشيف المضغوط")
        else:
            print("\n❌ فشل في إنشاء المثبت!")
        
        return success
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    exit(0 if success else 1)
