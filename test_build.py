#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار البناء والتحقق من صحة الملفات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import subprocess
from pathlib import Path

def test_python_version():
    """اختبار إصدار Python"""
    print("🐍 اختبار إصدار Python...")
    version = sys.version_info
    print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ⚠️  تحذير: يُنصح باستخدام Python 3.8 أو أحدث")
        return False
    else:
        print("   ✅ إصدار Python مناسب")
        return True

def test_required_files():
    """اختبار وجود الملفات المطلوبة"""
    print("📁 اختبار وجود الملفات المطلوبة...")
    
    required_files = [
        'server_manager.py',
        'app/__init__.py',
        'config.py',
        'schema.sql',
        'requirements.txt',
        'requirements_build.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"   ❌ مفقود: {file_path}")
        else:
            print(f"   ✅ موجود: {file_path}")
    
    if missing_files:
        print(f"   ⚠️  ملفات مفقودة: {len(missing_files)}")
        return False
    else:
        print("   ✅ جميع الملفات المطلوبة موجودة")
        return True

def test_imports():
    """اختبار استيراد المكتبات"""
    print("📦 اختبار استيراد المكتبات...")
    
    libraries = [
        ('flask', 'Flask'),
        ('PyQt5.QtWidgets', 'PyQt5'),
        ('qrcode', 'QR Code'),
        ('bcrypt', 'bcrypt'),
        ('PIL', 'Pillow')
    ]
    
    failed_imports = []
    for lib, name in libraries:
        try:
            __import__(lib)
            print(f"   ✅ {name}")
        except ImportError as e:
            failed_imports.append((name, str(e)))
            print(f"   ❌ {name} - {e}")
    
    if failed_imports:
        print(f"   ⚠️  مكتبات غير مثبتة: {len(failed_imports)}")
        return False
    else:
        print("   ✅ جميع المكتبات مثبتة")
        return True

def test_pyinstaller():
    """اختبار PyInstaller"""
    print("🔧 اختبار PyInstaller...")
    
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print(f"   ✅ PyInstaller مثبت - الإصدار: {version}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("   ❌ PyInstaller غير مثبت")
        return False

def test_app_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("🚀 اختبار بدء تشغيل التطبيق...")
    
    try:
        # محاولة استيراد الوحدات الأساسية
        sys.path.insert(0, '.')
        
        # اختبار استيراد path_manager
        try:
            import path_manager
            print("   ✅ path_manager")
        except ImportError as e:
            print(f"   ⚠️  path_manager - {e}")
        
        # اختبار استيراد exe_config
        try:
            import exe_config
            print("   ✅ exe_config")
        except ImportError as e:
            print(f"   ⚠️  exe_config - {e}")
        
        # اختبار استيراد app
        try:
            from app import create_app
            print("   ✅ Flask app")
        except ImportError as e:
            print(f"   ❌ Flask app - {e}")
            return False
        
        print("   ✅ التطبيق يمكن تشغيله")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في بدء التشغيل: {e}")
        return False

def test_build_environment():
    """اختبار بيئة البناء"""
    print("🏗️  اختبار بيئة البناء...")
    
    # اختبار وجود ملفات البناء
    build_files = [
        'build.py',
        'build_simple.py',
        'setup_environment.py',
        'run_build.bat'
    ]
    
    for file_path in build_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ⚠️  {file_path} - غير موجود")
    
    return True

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات البناء")
    print("=" * 50)
    
    tests = [
        ("إصدار Python", test_python_version),
        ("الملفات المطلوبة", test_required_files),
        ("المكتبات", test_imports),
        ("PyInstaller", test_pyinstaller),
        ("بدء التشغيل", test_app_startup),
        ("بيئة البناء", test_build_environment)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبارات:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! يمكنك البناء الآن.")
        return True
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى إصلاح المشاكل أولاً.")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n💡 للبناء، استخدم أحد الأوامر التالية:")
            print("   - run_build.bat")
            print("   - python build.py")
            print("   - python build_simple.py")
        else:
            print("\n🔧 لإصلاح المشاكل:")
            print("   - python setup_environment.py")
            print("   - pip install -r requirements_build.txt")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
