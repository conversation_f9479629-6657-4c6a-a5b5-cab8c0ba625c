#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أداة لإعادة تعيين كلمة المرور الافتراضية للمستخدم الأدمن
"""

import os
import sys
import sqlite3
import bcrypt
from datetime import datetime

def reset_admin_password():
    """إعادة تعيين كلمة المرور الافتراضية للمستخدم الأدمن"""
    
    print("أداة إعادة تعيين كلمة المرور الافتراضية")
    print("=" * 40)
    
    # تحديد مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'realestate.db')
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        print(f"خطأ: قاعدة البيانات غير موجودة في المسار: {db_path}")
        
        # إنشاء مجلد instance إذا لم يكن موجودًا
        instance_dir = os.path.dirname(db_path)
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)
            print(f"تم إنشاء مجلد: {instance_dir}")
        
        # البحث عن ملف schema.sql
        schema_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'schema.sql')
        if not os.path.exists(schema_path):
            print(f"خطأ: ملف schema.sql غير موجود في المسار: {schema_path}")
            
            # البحث عن الملف في مجلدات مختلفة
            possible_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), '_internal', 'schema.sql'),
                'schema.sql'
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    schema_path = path
                    print(f"تم العثور على ملف schema.sql في المسار: {schema_path}")
                    break
            else:
                print("خطأ: لم يتم العثور على ملف schema.sql")
                return False
        
        # إنشاء قاعدة البيانات
        print(f"إنشاء قاعدة بيانات جديدة في المسار: {db_path}")
        conn = sqlite3.connect(db_path)
        
        # قراءة ملف schema.sql
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # تنفيذ استعلامات إنشاء قاعدة البيانات
        conn.executescript(schema_sql)
        conn.commit()
        print("تم إنشاء قاعدة البيانات بنجاح")
    
    # الاتصال بقاعدة البيانات
    print(f"الاتصال بقاعدة البيانات: {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # التحقق من وجود جدول المستخدمين
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    if not cursor.fetchone():
        print("خطأ: جدول المستخدمين غير موجود في قاعدة البيانات")
        return False
    
    # تحديد بيانات المستخدم الأدمن
    username = "admin"
    password = "admin123"
    name = "مدير النظام"
    email = "<EMAIL>"
    role = "admin"
    
    # تشفير كلمة المرور
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    # التحقق من وجود المستخدم الأدمن
    cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
    admin_user = cursor.fetchone()
    
    if admin_user:
        # تحديث كلمة المرور للمستخدم الموجود
        admin_id = admin_user[0]
        cursor.execute(
            "UPDATE users SET password = ?, name = ?, email = ?, role = ? WHERE id = ?",
            (hashed_password, name, email, role, admin_id)
        )
        conn.commit()
        print(f"تم تحديث كلمة المرور للمستخدم: {username}")
    else:
        # إنشاء مستخدم أدمن جديد
        cursor.execute(
            "INSERT INTO users (username, password, name, email, role, created_at) VALUES (?, ?, ?, ?, ?, ?)",
            (username, hashed_password, name, email, role, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        )
        conn.commit()
        print(f"تم إنشاء مستخدم أدمن جديد: {username}")
    
    # إغلاق الاتصال بقاعدة البيانات
    conn.close()
    
    print("\nتم إعادة تعيين كلمة المرور بنجاح!")
    print(f"اسم المستخدم: {username}")
    print(f"كلمة المرور: {password}")
    
    return True

if __name__ == "__main__":
    # تعيين المجلد الحالي للتطبيق
    if getattr(sys, 'frozen', False):
        # في حالة التشغيل كملف تنفيذي
        application_path = os.path.dirname(sys.executable)
        os.chdir(application_path)
    else:
        # في حالة التشغيل كسكريبت
        application_path = os.path.dirname(os.path.abspath(__file__))
        os.chdir(application_path)
    
    # إعادة تعيين كلمة المرور
    reset_admin_password()
    
    # الانتظار حتى يضغط المستخدم على أي مفتاح
    input("\nاضغط على أي مفتاح للخروج...")
