@echo off
echo أداة إعادة تعيين كلمة المرور الافتراضية
echo =====================================

REM تعيين المجلد الحالي
cd /d "%~dp0"

REM إضافة المجلد الحالي إلى مسار البحث
set PYTHONPATH=%PYTHONPATH%;%CD%

REM إضافة مجلد _internal إلى مسار البحث إذا كان موجودًا
if exist "%CD%\_internal" (
    set PYTHONPATH=%PYTHONPATH%;%CD%\_internal
)

REM تشغيل سكريبت إعادة تعيين كلمة المرور
python reset_password.py

REM إذا لم يكن Python مثبتًا، نحاول استخدام النسخة المضمنة
if %ERRORLEVEL% NEQ 0 (
    echo محاولة استخدام Python المضمنة...
    if exist "%CD%\_internal\python.exe" (
        "%CD%\_internal\python.exe" reset_password.py
    ) else (
        echo خطأ: لم يتم العثور على Python. يرجى تثبيت Python أو استخدام النسخة المضمنة.
        pause
        exit /b 1
    )
)

pause
