#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت بناء وتحويل المشروع إلى ملف EXE

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path

class ProjectBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "server_manager.spec"
        
    def clean_build(self):
        """تنظيف ملفات البناء السابقة"""
        print("🧹 تنظيف ملفات البناء السابقة...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"   ✅ تم حذف: {dir_path}")
        
        # حذف ملف .spec إذا كان موجوداً
        if self.spec_file.exists():
            self.spec_file.unlink()
            print(f"   ✅ تم حذف: {self.spec_file}")
    
    def install_dependencies(self):
        """تثبيت المكتبات المطلوبة"""
        print("📦 تثبيت المكتبات المطلوبة...")
        
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements_build.txt"
            ], check=True)
            print("   ✅ تم تثبيت المكتبات بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ فشل في تثبيت المكتبات: {e}")
            return False
        return True
    
    def create_spec_file(self):
        """إنشاء ملف .spec مخصص"""
        print("📝 إنشاء ملف التكوين...")
        
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# تحديد المسار الأساسي للمشروع
project_root = Path(__file__).parent

# قائمة الملفات والمجلدات المطلوبة
datas = [
    (str(project_root / 'app'), 'app'),
    (str(project_root / 'schema.sql'), '.'),
    (str(project_root / 'config.py'), '.'),
    (str(project_root / 'file.jpeg'), '.'),
    (str(project_root / 'file.ico'), '.'),
    (str(project_root / 'monkey_patch.py'), '.'),
    (str(project_root / 'fix_paths.py'), '.'),
]

# إضافة مجلد templates إذا كان موجوداً
templates_path = project_root / 'app' / 'templates'
if templates_path.exists():
    datas.append((str(templates_path), 'app/templates'))

# إضافة مجلد static إذا كان موجوداً
static_path = project_root / 'app' / 'static'
if static_path.exists():
    datas.append((str(static_path), 'app/static'))

# قائمة الوحدات المخفية المطلوبة
hiddenimports = [
    'flask',
    'flask_login',
    'flask_wtf',
    'flask_bcrypt',
    'flask_mail',
    'werkzeug',
    'jinja2',
    'itsdangerous',
    'email_validator',
    'PIL',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'qrcode',
    'bcrypt',
    'sqlite3',
    'urllib.parse',
    'importlib.util',
    'app',
    'app.auth',
    'app.dashboard',
    'app.owners',
    'app.properties',
    'app.tenants',
    'app.documents',
    'app.finance',
    'app.reports',
    'app.db',
    'app.models',
    'app.forms',
    'app.utils',
    'app.decorators',
]

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='مدير_خادم_مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'file.ico') if (project_root / 'file.ico').exists() else None,
)
'''
        
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"   ✅ تم إنشاء ملف التكوين: {self.spec_file}")
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("🔨 بناء الملف التنفيذي...")
        
        try:
            cmd = [
                "pyinstaller",
                "--clean",
                "--noconfirm",
                str(self.spec_file)
            ]
            
            print(f"   🔧 تشغيل الأمر: {' '.join(cmd)}")
            subprocess.run(cmd, check=True)
            print("   ✅ تم بناء الملف التنفيذي بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"   ❌ فشل في بناء الملف التنفيذي: {e}")
            return False
    
    def create_portable_package(self):
        """إنشاء حزمة محمولة"""
        print("📦 إنشاء الحزمة المحمولة...")
        
        # إنشاء مجلد الحزمة
        package_dir = self.project_root / "مكتب_عصام_الفت_محمول"
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()
        
        # نسخ الملف التنفيذي
        exe_file = self.dist_dir / "مدير_خادم_مكتب_عصام_الفت.exe"
        if exe_file.exists():
            shutil.copy2(exe_file, package_dir)
            print(f"   ✅ تم نسخ الملف التنفيذي")
        
        # إنشاء مجلدات ضرورية
        essential_dirs = ['instance', 'uploads', 'uploads/buildings', 'uploads/contracts', 
                         'uploads/documents', 'uploads/owners', 'uploads/tenants', 'uploads/transactions']
        
        for dir_name in essential_dirs:
            (package_dir / dir_name).mkdir(parents=True, exist_ok=True)
        
        # إنشاء ملف README
        readme_content = """
# مكتب عصام الفت لإدارة الأملاك - النسخة المحمولة

## طريقة التشغيل:
1. قم بتشغيل ملف "مدير_خادم_مكتب_عصام_الفت.exe"
2. اختر نوع الخادم (محلي أو شبكة)
3. اضغط على "تشغيل الخادم"
4. سيتم فتح المتصفح تلقائياً

## معلومات تسجيل الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin123

## ملاحظات مهمة:
- تأكد من السماح للتطبيق في جدار الحماية
- لا تحذف مجلدات instance أو uploads
- يمكنك نسخ المجلد كاملاً إلى أي مكان آخر

## الدعم الفني:
- فيسبوك: https://www.facebook.com/almbarmg
- واتساب: 0201032540807

تم التطوير بواسطة شركة المبرمج المصري
جميع الحقوق محفوظة © 2025
"""
        
        with open(package_dir / "اقرأني.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"   ✅ تم إنشاء الحزمة المحمولة: {package_dir}")
        return package_dir
    
    def build(self):
        """تشغيل عملية البناء الكاملة"""
        print("🚀 بدء عملية بناء المشروع...")
        print("=" * 50)
        
        # التحقق من نظام التشغيل
        if platform.system() != "Windows":
            print("⚠️  تحذير: هذا السكريبت مصمم لنظام Windows")
        
        # تنظيف ملفات البناء السابقة
        self.clean_build()
        
        # تثبيت المكتبات
        if not self.install_dependencies():
            return False
        
        # إنشاء ملف التكوين
        self.create_spec_file()
        
        # بناء الملف التنفيذي
        if not self.build_executable():
            return False
        
        # إنشاء الحزمة المحمولة
        package_dir = self.create_portable_package()
        
        print("=" * 50)
        print("🎉 تم إنجاز عملية البناء بنجاح!")
        print(f"📁 مجلد الحزمة المحمولة: {package_dir}")
        print("💡 يمكنك الآن نسخ المجلد إلى أي جهاز وتشغيل التطبيق")
        
        return True

if __name__ == "__main__":
    builder = ProjectBuilder()
    success = builder.build()
    
    if success:
        print("\n✅ تم البناء بنجاح!")
        input("اضغط Enter للخروج...")
    else:
        print("\n❌ فشل في البناء!")
        input("اضغط Enter للخروج...")
        sys.exit(1)
