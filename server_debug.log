2025-05-11 06:19:57,520 - server_manager - INFO - Applying path fixes...
2025-05-11 06:19:57,521 - server_manager - WARNING - Path fix file not found at: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:20:01,247 - app_init - INFO - Applying path fixes...
2025-05-11 06:20:01,248 - app_init - WARNING - Path fix file not found at: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:20:01,248 - app_init - INFO - Applying direct path fixes...
2025-05-11 06:20:01,249 - app_init - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:49,243 - server_manager - INFO - Applying path fixes...
2025-05-11 06:20:49,243 - server_manager - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:20:49,247 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:20:49,248 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:49,248 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:20:49,248 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:20:49,248 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:20:49,248 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:20:49,249 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:20:49,249 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:20:49,249 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:20:49,249 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:20:49,249 - fix_paths - INFO - Added H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app to sys.path
2025-05-11 06:20:49,250 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:49,250 - fix_paths - INFO - Python search path:
2025-05-11 06:20:49,250 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:20:49,250 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:49,250 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:20:49,251 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:20:49,251 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:20:49,251 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:20:49,251 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:20:49,252 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:20:49,252 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:20:49,252 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:20:49,253 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:20:49,253 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:20:52,045 - app_init - INFO - Applying path fixes...
2025-05-11 06:20:52,045 - app_init - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:20:52,055 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:20:52,055 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:52,055 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:20:52,055 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:20:52,056 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:20:52,056 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:20:52,056 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:20:52,056 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:20:52,057 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:20:52,057 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:20:52,057 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:52,057 - fix_paths - INFO - Python search path:
2025-05-11 06:20:52,057 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:20:52,058 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:20:52,058 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:20:52,058 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:20:52,058 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:20:52,058 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:20:52,059 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:20:52,059 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:20:52,059 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:20:52,059 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:20:52,059 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:20:52,060 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:21:51,484 - server_manager - INFO - Applying path fixes...
2025-05-11 06:21:51,485 - server_manager - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:21:51,490 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:21:51,490 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:51,490 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:21:51,490 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:21:51,490 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:21:51,490 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:21:51,491 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:21:51,491 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:21:51,491 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:21:51,491 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:21:51,491 - fix_paths - INFO - Added H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app to sys.path
2025-05-11 06:21:51,491 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:51,492 - fix_paths - INFO - Python search path:
2025-05-11 06:21:51,492 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:21:51,492 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:51,492 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:21:51,492 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:21:51,492 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:21:51,492 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:21:51,492 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:21:51,493 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:21:51,493 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:21:51,493 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:21:51,493 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:21:51,493 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:21:54,353 - app_init - INFO - Applying path fixes...
2025-05-11 06:21:54,353 - app_init - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:21:54,354 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:21:54,354 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:54,355 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:21:54,355 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:21:54,355 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:21:54,355 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:21:54,356 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:21:54,356 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:21:54,356 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:21:54,356 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:21:54,357 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:54,357 - fix_paths - INFO - Python search path:
2025-05-11 06:21:54,357 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:21:54,357 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:21:54,358 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:21:54,358 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:21:54,358 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:21:54,358 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:21:54,358 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:21:54,359 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:21:54,359 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:21:54,359 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:21:54,359 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:21:54,359 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:22:58,254 - server_manager - INFO - Applying path fixes...
2025-05-11 06:22:58,254 - server_manager - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:22:58,258 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:22:58,258 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:22:58,259 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:22:58,259 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:22:58,259 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:22:58,259 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:22:58,260 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:22:58,260 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:22:58,260 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:22:58,260 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:22:58,260 - fix_paths - INFO - Added H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app to sys.path
2025-05-11 06:22:58,261 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:22:58,261 - fix_paths - INFO - Python search path:
2025-05-11 06:22:58,261 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:22:58,262 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:22:58,262 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:22:58,262 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:22:58,263 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:22:58,263 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:22:58,263 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:22:58,263 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:22:58,264 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:22:58,264 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:22:58,264 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:22:58,264 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:23:01,554 - app_init - INFO - Applying path fixes...
2025-05-11 06:23:01,555 - app_init - INFO - Loading path fixes from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\fix_paths.py
2025-05-11 06:23:01,556 - fix_paths - INFO - Starting path fixing process...
2025-05-11 06:23:01,556 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:23:01,557 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\instance
2025-05-11 06:23:01,557 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads
2025-05-11 06:23:01,557 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\buildings
2025-05-11 06:23:01,558 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\contracts
2025-05-11 06:23:01,558 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\documents
2025-05-11 06:23:01,559 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\owners
2025-05-11 06:23:01,559 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\tenants
2025-05-11 06:23:01,559 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\uploads\transactions
2025-05-11 06:23:01,560 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:23:01,560 - fix_paths - INFO - Python search path:
2025-05-11 06:23:01,561 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود\app
2025-05-11 06:23:01,561 - fix_paths - INFO -   - H:\مشاريع 2025\مشروع العقارات (عصام)\السورس كود
2025-05-11 06:23:01,561 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-11 06:23:01,562 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-11 06:23:01,562 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-11 06:23:01,563 - fix_paths - INFO -   - C:\Python313
2025-05-11 06:23:01,563 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-11 06:23:01,563 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-11 06:23:01,564 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-11 06:23:01,564 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-11 06:23:01,564 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-11 06:23:01,565 - fix_paths - INFO - Path fixing process completed successfully
2025-05-11 06:23:03,957 - db - DEBUG - Executing query: SELECT * FROM users WHERE id = ?
2025-05-11 06:23:03,958 - db - DEBUG - Query args: ['1']
2025-05-11 06:23:03,958 - db - INFO - Database path: instance/realestate.db
2025-05-11 06:23:03,959 - db - INFO - Database directory: instance
2025-05-11 06:23:03,959 - db - INFO - Ensured database directory exists: instance
2025-05-11 06:23:03,960 - db - INFO - Registered datetime adapters and converters
2025-05-11 06:23:03,960 - db - INFO - Connecting to database: instance/realestate.db
2025-05-11 06:23:03,962 - db - INFO - Database connection established
2025-05-11 06:23:03,962 - db - INFO - Database configuration applied
2025-05-11 06:23:03,963 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,963 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,963 - db - DEBUG - Executing query: SELECT COUNT(*) as count FROM owners
2025-05-11 06:23:03,964 - db - DEBUG - Query args: []
2025-05-11 06:23:03,964 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,964 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,964 - db - DEBUG - Executing query: SELECT COUNT(*) as count FROM buildings
2025-05-11 06:23:03,964 - db - DEBUG - Query args: []
2025-05-11 06:23:03,965 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,965 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,965 - db - DEBUG - Executing query: SELECT COUNT(*) as count FROM units
2025-05-11 06:23:03,965 - db - DEBUG - Query args: []
2025-05-11 06:23:03,966 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,966 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,966 - db - DEBUG - Executing query: SELECT COUNT(*) as count FROM tenants
2025-05-11 06:23:03,966 - db - DEBUG - Query args: []
2025-05-11 06:23:03,967 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,967 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,968 - db - DEBUG - Executing query: SELECT COUNT(*) as count FROM contracts WHERE status = "active"
2025-05-11 06:23:03,968 - db - DEBUG - Query args: []
2025-05-11 06:23:03,968 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,968 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,969 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,969 - db - DEBUG - Query args: ['2025-05-01', '2025-05-31']
2025-05-11 06:23:03,970 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,970 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,971 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,972 - db - DEBUG - Query args: ['2025-05-01', '2025-05-31']
2025-05-11 06:23:03,973 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,973 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,974 - db - DEBUG - Executing query: 
    SELECT c.*, t.name as tenant_name, u.unit_number, b.name as building_name
    FROM contracts c
    JOIN tenants t ON c.tenant_id = t.id
    JOIN units u ON c.unit_id = u.id
    JOIN buildings b ON u.building_id = b.id
    WHERE c.end_date BETWEEN ? AND ?
    AND c.status = 'active'
    
2025-05-11 06:23:03,974 - db - DEBUG - Query args: ['2025-05-11', '2025-05-18']
2025-05-11 06:23:03,975 - db - DEBUG - Query returned 0 rows
2025-05-11 06:23:03,975 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,975 - db - DEBUG - Query args: ['2025-01-01', '2025-01-31']
2025-05-11 06:23:03,975 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,976 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,976 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,976 - db - DEBUG - Query args: ['2025-02-01', '2025-02-28']
2025-05-11 06:23:03,976 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,977 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,977 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,977 - db - DEBUG - Query args: ['2025-03-01', '2025-03-31']
2025-05-11 06:23:03,977 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,977 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,978 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,978 - db - DEBUG - Query args: ['2025-04-01', '2025-04-30']
2025-05-11 06:23:03,978 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,978 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,978 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,979 - db - DEBUG - Query args: ['2025-05-01', '2025-05-31']
2025-05-11 06:23:03,979 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,979 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,979 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,979 - db - DEBUG - Query args: ['2025-06-01', '2025-06-30']
2025-05-11 06:23:03,980 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,980 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,980 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,980 - db - DEBUG - Query args: ['2025-07-01', '2025-07-31']
2025-05-11 06:23:03,981 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,981 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,981 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,981 - db - DEBUG - Query args: ['2025-08-01', '2025-08-31']
2025-05-11 06:23:03,981 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,981 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,982 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,982 - db - DEBUG - Query args: ['2025-09-01', '2025-09-30']
2025-05-11 06:23:03,983 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,983 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,983 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,983 - db - DEBUG - Query args: ['2025-10-01', '2025-10-31']
2025-05-11 06:23:03,984 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,984 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,984 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,985 - db - DEBUG - Query args: ['2025-11-01', '2025-11-30']
2025-05-11 06:23:03,985 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,985 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,985 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    
2025-05-11 06:23:03,986 - db - DEBUG - Query args: ['2025-12-01', '2025-12-31']
2025-05-11 06:23:03,986 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,986 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,987 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,988 - db - DEBUG - Query args: ['2025-01-01', '2025-01-31']
2025-05-11 06:23:03,988 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,989 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,989 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,989 - db - DEBUG - Query args: ['2025-02-01', '2025-02-28']
2025-05-11 06:23:03,989 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,990 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,990 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,990 - db - DEBUG - Query args: ['2025-03-01', '2025-03-31']
2025-05-11 06:23:03,990 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,990 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,991 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,991 - db - DEBUG - Query args: ['2025-04-01', '2025-04-30']
2025-05-11 06:23:03,991 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,991 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,992 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,992 - db - DEBUG - Query args: ['2025-05-01', '2025-05-31']
2025-05-11 06:23:03,992 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,992 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,993 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,993 - db - DEBUG - Query args: ['2025-06-01', '2025-06-30']
2025-05-11 06:23:03,993 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,993 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,993 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,993 - db - DEBUG - Query args: ['2025-07-01', '2025-07-31']
2025-05-11 06:23:03,994 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,994 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,994 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,994 - db - DEBUG - Query args: ['2025-08-01', '2025-08-31']
2025-05-11 06:23:03,995 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,995 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,995 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,995 - db - DEBUG - Query args: ['2025-09-01', '2025-09-30']
2025-05-11 06:23:03,995 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,995 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,996 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,996 - db - DEBUG - Query args: ['2025-10-01', '2025-10-31']
2025-05-11 06:23:03,996 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,996 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,996 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,996 - db - DEBUG - Query args: ['2025-11-01', '2025-11-30']
2025-05-11 06:23:03,997 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,997 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,997 - db - DEBUG - Executing query: 
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    
2025-05-11 06:23:03,997 - db - DEBUG - Query args: ['2025-12-01', '2025-12-31']
2025-05-11 06:23:03,998 - db - DEBUG - Query returned 1 rows
2025-05-11 06:23:03,998 - db - DEBUG - Returning single row: Found
2025-05-11 06:23:03,998 - db - DEBUG - Executing query: 
        SELECT t.*, u.name as created_by_name 
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        ORDER BY t.transaction_date DESC
        LIMIT 5
    
2025-05-11 06:23:03,998 - db - DEBUG - Query args: []
2025-05-11 06:23:03,998 - db - DEBUG - Query returned 0 rows
2025-05-11 06:23:03,999 - db - DEBUG - Executing query: 
        SELECT m.*, u.unit_number, b.name as building_name
        FROM maintenance_requests m
        JOIN units u ON m.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        ORDER BY m.request_date DESC
        LIMIT 5
    
2025-05-11 06:23:03,999 - db - DEBUG - Query args: []
2025-05-11 06:23:03,999 - db - DEBUG - Query returned 0 rows
2025-05-11 06:23:04,085 - werkzeug - INFO - 127.0.0.1 - - [11/May/2025 06:23:04] "GET / HTTP/1.1" 200 -
2025-05-11 06:23:04,358 - werkzeug - INFO - 127.0.0.1 - - [11/May/2025 06:23:04] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-11 06:23:04,411 - werkzeug - INFO - 127.0.0.1 - - [11/May/2025 06:23:04] "GET /static/js/main.js HTTP/1.1" 200 -
2025-05-30 06:17:48,863 - server_manager - INFO - Applying path fixes...
2025-05-30 06:17:48,864 - server_manager - INFO - Loading path fixes from: H:\مشاريع 2025\asam\New folder\sorce code\fix_paths.py
2025-05-30 06:17:48,865 - fix_paths - INFO - Starting path fixing process...
2025-05-30 06:17:48,866 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:48,866 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\instance
2025-05-30 06:17:48,867 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads
2025-05-30 06:17:48,867 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\buildings
2025-05-30 06:17:48,868 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\contracts
2025-05-30 06:17:48,868 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\documents
2025-05-30 06:17:48,868 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\owners
2025-05-30 06:17:48,869 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\tenants
2025-05-30 06:17:48,870 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\transactions
2025-05-30 06:17:48,870 - fix_paths - INFO - Added H:\مشاريع 2025\asam\New folder\sorce code\app to sys.path
2025-05-30 06:17:48,871 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:48,871 - fix_paths - INFO - Python search path:
2025-05-30 06:17:48,871 - fix_paths - INFO -   - H:\مشاريع 2025\asam\New folder\sorce code\app
2025-05-30 06:17:48,872 - fix_paths - INFO -   - H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:48,872 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-30 06:17:48,872 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-30 06:17:48,873 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-30 06:17:48,874 - fix_paths - INFO -   - C:\Python313
2025-05-30 06:17:48,875 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-30 06:17:48,876 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-30 06:17:48,876 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-30 06:17:48,876 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-30 06:17:48,877 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-30 06:17:48,877 - fix_paths - INFO - Path fixing process completed successfully
2025-05-30 06:17:55,243 - app_init - INFO - Applying path fixes...
2025-05-30 06:17:55,243 - app_init - INFO - Loading path fixes from: H:\مشاريع 2025\asam\New folder\sorce code\fix_paths.py
2025-05-30 06:17:55,244 - fix_paths - INFO - Starting path fixing process...
2025-05-30 06:17:55,244 - fix_paths - INFO - Running as script from: H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:55,244 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\instance
2025-05-30 06:17:55,244 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads
2025-05-30 06:17:55,245 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\buildings
2025-05-30 06:17:55,245 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\contracts
2025-05-30 06:17:55,245 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\documents
2025-05-30 06:17:55,245 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\owners
2025-05-30 06:17:55,245 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\tenants
2025-05-30 06:17:55,246 - fix_paths - INFO - Directory already exists: H:\مشاريع 2025\asam\New folder\sorce code\uploads\transactions
2025-05-30 06:17:55,246 - fix_paths - INFO - Changed current directory to: H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:55,246 - fix_paths - INFO - Python search path:
2025-05-30 06:17:55,246 - fix_paths - INFO -   - H:\مشاريع 2025\asam\New folder\sorce code\app
2025-05-30 06:17:55,246 - fix_paths - INFO -   - H:\مشاريع 2025\asam\New folder\sorce code
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Python313\python313.zip
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Python313\DLLs
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Python313\Lib
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Python313
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages
2025-05-30 06:17:55,247 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32
2025-05-30 06:17:55,248 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\win32\lib
2025-05-30 06:17:55,248 - fix_paths - INFO -   - C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\Pythonwin
2025-05-30 06:17:55,248 - fix_paths - INFO -   - C:\Python313\Lib\site-packages
2025-05-30 06:17:55,248 - fix_paths - INFO - Path fixing process completed successfully
2025-05-30 06:17:57,608 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 06:17:57] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-30 06:17:57,718 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 06:17:57] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-05-30 06:17:58,059 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 06:17:58] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-30 06:17:58,070 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 06:17:58] "GET /static/js/main.js HTTP/1.1" 200 -
2025-05-30 06:17:58,637 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 06:17:58] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
