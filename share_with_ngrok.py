"""
أداة مشاركة نظام إدارة مكتب العقارات مع العملاء باستخدام Ngrok

نظام إدارة مكتب العقارات
تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
import json
import requests
from urllib.parse import urlparse

def check_ngrok_installed():
    """التحقق من تثبيت Ngrok"""
    try:
        # محاولة تنفيذ أمر ngrok للتحقق من وجوده
        result = subprocess.run(['ngrok', '--version'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True,
                               creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except FileNotFoundError:
        return False

def download_ngrok():
    """تنزيل Ngrok إذا لم يكن موجودًا"""
    print("Ngrok غير مثبت. سيتم تنزيله الآن...")

    # إنشاء مجلد للتنزيل
    os.makedirs('tools', exist_ok=True)

    # تنزيل Ngrok
    import urllib.request
    ngrok_url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
    zip_path = os.path.join('tools', 'ngrok.zip')

    print("جاري تنزيل Ngrok...")
    urllib.request.urlretrieve(ngrok_url, zip_path)

    # فك ضغط الملف
    import zipfile
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall('tools')

    print("تم تنزيل Ngrok وفك ضغطه بنجاح!")

    # إضافة مجلد Ngrok إلى PATH مؤقتًا
    ngrok_path = os.path.abspath('tools')
    os.environ['PATH'] = ngrok_path + os.pathsep + os.environ['PATH']

    return os.path.join(ngrok_path, 'ngrok.exe')

def setup_ngrok_auth(auth_token=None):
    """إعداد مصادقة Ngrok"""
    if not auth_token:
        print("\n" + "="*50)
        print("ملاحظة هامة حول Ngrok:")
        print("للحصول على أفضل أداء وتجنب قيود الاستخدام المجاني،")
        print("يُنصح بإنشاء حساب على موقع Ngrok والحصول على رمز مصادقة.")
        print("1. قم بزيارة: https://dashboard.ngrok.com/signup")
        print("2. قم بإنشاء حساب مجاني")
        print("3. انسخ رمز المصادقة من: https://dashboard.ngrok.com/get-started/your-authtoken")
        print("="*50)

        auth_token = input("\nأدخل رمز مصادقة Ngrok (اضغط Enter للتخطي): ").strip()

    if auth_token:
        try:
            subprocess.run(['ngrok', 'config', 'add-authtoken', auth_token],
                          stdout=subprocess.PIPE,
                          stderr=subprocess.PIPE,
                          creationflags=subprocess.CREATE_NO_WINDOW)
            print("تم إعداد مصادقة Ngrok بنجاح!")
            return True
        except Exception as e:
            print(f"خطأ في إعداد مصادقة Ngrok: {str(e)}")
            return False

    return False

def run_flask_app():
    """تشغيل تطبيق Flask"""
    print("جاري تشغيل تطبيق Flask...")

    # تحديد مسار ملف run.py
    run_path = 'run.py'
    if not os.path.exists(run_path):
        run_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'run.py')

    if not os.path.exists(run_path):
        print(f"خطأ: ملف {run_path} غير موجود!")
        return None

    # تشغيل تطبيق Flask
    flask_process = subprocess.Popen([sys.executable, run_path],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    creationflags=subprocess.CREATE_NO_WINDOW)

    # انتظار بدء تشغيل التطبيق
    print("انتظار بدء تشغيل التطبيق...")
    time.sleep(5)

    return flask_process

def start_ngrok(port=5000):
    """بدء تشغيل Ngrok"""
    print(f"جاري إنشاء نفق Ngrok على المنفذ {port}...")

    # التحقق من عدم وجود عملية Ngrok قيد التشغيل
    try:
        # محاولة إيقاف أي عمليات Ngrok قيد التشغيل
        subprocess.run(['taskkill', '/F', '/IM', 'ngrok.exe'],
                      stdout=subprocess.PIPE,
                      stderr=subprocess.PIPE,
                      creationflags=subprocess.CREATE_NO_WINDOW)
        print("تم إيقاف عمليات Ngrok السابقة.")
    except:
        pass

    # بدء تشغيل Ngrok بطريقة تسمح بعرض الواجهة
    ngrok_process = subprocess.Popen(['ngrok', 'http', str(port)],
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    creationflags=subprocess.CREATE_NO_WINDOW)

    # انتظار بدء تشغيل Ngrok
    print("انتظار بدء تشغيل Ngrok...")
    time.sleep(5)  # زيادة وقت الانتظار

    return ngrok_process

def get_ngrok_url():
    """الحصول على عنوان URL من Ngrok"""
    # طريقة 1: استخدام واجهة برمجة تطبيقات Ngrok المحلية
    try:
        # الاتصال بواجهة برمجة تطبيقات Ngrok المحلية
        response = requests.get('http://localhost:4040/api/tunnels', timeout=2)
        data = response.json()

        # استخراج عنوان URL
        for tunnel in data['tunnels']:
            if tunnel['proto'] == 'https':
                return tunnel['public_url']

        # إذا لم يتم العثور على نفق HTTPS، استخدم أول نفق
        if data['tunnels']:
            return data['tunnels'][0]['public_url']
    except Exception as e:
        print(f"محاولة استخدام الطريقة البديلة للحصول على عنوان URL...")

    # طريقة 2: قراءة مخرجات Ngrok مباشرة
    try:
        # تشغيل أمر Ngrok للحصول على قائمة الأنفاق
        result = subprocess.run(['ngrok', 'status'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True,
                               creationflags=subprocess.CREATE_NO_WINDOW)

        # البحث عن عنوان URL في المخرجات
        output = result.stdout
        lines = output.split('\n')

        for line in lines:
            if 'URL:' in line and ('https://' in line or 'http://' in line):
                url = line.split('URL:')[1].strip()
                return url
    except Exception as e:
        print(f"فشل في الحصول على عنوان URL من مخرجات Ngrok: {str(e)}")

    # طريقة 3: فتح واجهة Ngrok في المتصفح
    try:
        print("لم نتمكن من الحصول على عنوان URL تلقائيًا.")
        print("سنفتح واجهة Ngrok في المتصفح. يرجى نسخ عنوان URL من هناك.")
        webbrowser.open('http://localhost:4040')

        # طلب إدخال عنوان URL يدويًا
        url = input("يرجى نسخ عنوان URL من المتصفح وإدخاله هنا: ")
        if url and ('https://' in url or 'http://' in url):
            return url
    except Exception as e:
        print(f"فشل في فتح واجهة Ngrok في المتصفح: {str(e)}")

    return None

def monitor_processes(flask_process, ngrok_process):
    """مراقبة العمليات وإظهار السجلات"""
    try:
        while flask_process.poll() is None and ngrok_process.poll() is None:
            # قراءة سجلات Flask
            flask_output = flask_process.stdout.readline().decode('utf-8', errors='ignore').strip()
            if flask_output:
                print(f"[Flask] {flask_output}")

            # قراءة سجلات Ngrok
            ngrok_output = ngrok_process.stdout.readline().decode('utf-8', errors='ignore').strip()
            if ngrok_output:
                print(f"[Ngrok] {ngrok_output}")

            time.sleep(0.1)
    except KeyboardInterrupt:
        print("\nتم إيقاف المراقبة.")
    finally:
        # إيقاف العمليات عند الخروج
        flask_process.terminate()
        ngrok_process.terminate()

def share_with_ngrok():
    """مشاركة التطبيق باستخدام Ngrok"""
    print("="*50)
    print("مشاركة نظام إدارة مكتب العقارات مع العملاء")
    print("="*50)

    # التحقق من تثبيت Ngrok
    if not check_ngrok_installed():
        ngrok_path = download_ngrok()
        if not ngrok_path or not os.path.exists(ngrok_path):
            print("فشل في تنزيل Ngrok. يرجى تنزيله يدويًا من https://ngrok.com/download")
            return

    # إعداد مصادقة Ngrok
    setup_ngrok_auth()

    # تشغيل تطبيق Flask
    flask_process = run_flask_app()
    if not flask_process:
        print("فشل في تشغيل تطبيق Flask.")
        return

    # بدء تشغيل Ngrok
    ngrok_process = start_ngrok()
    if not ngrok_process:
        print("فشل في بدء تشغيل Ngrok.")
        flask_process.terminate()
        return

    # فتح واجهة Ngrok في المتصفح مباشرة
    print("\nجاري فتح واجهة Ngrok في المتصفح...")
    try:
        webbrowser.open('http://localhost:4040')
        print("تم فتح واجهة Ngrok في المتصفح.")
        print("يمكنك نسخ عنوان URL من المتصفح ومشاركته مع العملاء.")
    except Exception as e:
        print(f"فشل في فتح واجهة Ngrok في المتصفح: {str(e)}")

    # الحصول على عنوان URL من Ngrok
    print("\nجاري محاولة الحصول على عنوان URL تلقائيًا...")
    ngrok_url = None
    for i in range(3):  # محاولة الحصول على عنوان URL عدة مرات
        print(f"المحاولة {i+1} من 3...")
        ngrok_url = get_ngrok_url()
        if ngrok_url:
            break
        time.sleep(2)

    if not ngrok_url:
        print("\nلم نتمكن من الحصول على عنوان URL تلقائيًا.")
        print("يرجى نسخ عنوان URL من واجهة Ngrok في المتصفح.")
        print("واجهة Ngrok متاحة على: http://localhost:4040")

        # طلب إدخال عنوان URL يدويًا
        ngrok_url = input("\nيرجى نسخ عنوان URL من المتصفح وإدخاله هنا (أو اضغط Enter للتخطي): ")
        if not ngrok_url:
            print("تم تخطي إدخال عنوان URL.")
            print("التطبيق يعمل الآن ويمكن الوصول إليه من خلال واجهة Ngrok.")
            print("يرجى نسخ عنوان URL من واجهة Ngrok في المتصفح ومشاركته مع العملاء.")

    # عرض عنوان URL
    print("\n" + "="*50)
    if ngrok_url:
        print(f"تم إنشاء رابط المشاركة بنجاح!")
        print(f"الرابط: {ngrok_url}")
        print("يمكنك مشاركة هذا الرابط مع العملاء للوصول إلى التطبيق.")

        # فتح الرابط في المتصفح
        try:
            webbrowser.open(ngrok_url)
            print("تم فتح الرابط في المتصفح.")
        except Exception as e:
            print(f"فشل في فتح الرابط في المتصفح: {str(e)}")
    else:
        print("التطبيق يعمل الآن ويمكن الوصول إليه من خلال واجهة Ngrok.")
        print("يرجى نسخ عنوان URL من واجهة Ngrok في المتصفح ومشاركته مع العملاء.")
        print("واجهة Ngrok متاحة على: http://localhost:4040")

    print("\nبيانات تسجيل الدخول الافتراضية:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("="*50)

    print("\nاضغط Ctrl+C لإيقاف المشاركة وإغلاق التطبيق.")

    # بدء مراقبة العمليات في خيط منفصل
    monitor_thread = threading.Thread(target=monitor_processes, args=(flask_process, ngrok_process))
    monitor_thread.daemon = True
    monitor_thread.start()

    try:
        # الانتظار حتى يتم إيقاف العمليات
        while flask_process.poll() is None and ngrok_process.poll() is None:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nتم إيقاف المشاركة.")
    finally:
        # إيقاف العمليات عند الخروج
        flask_process.terminate()
        ngrok_process.terminate()
        print("تم إغلاق التطبيق والمشاركة.")

if __name__ == '__main__':
    share_with_ngrok()
