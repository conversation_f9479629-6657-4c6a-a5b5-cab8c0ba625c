#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معلومات الإصدار للتطبيق

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

# معلومات التطبيق
APP_NAME = "مدير خادم مكتب عصام الفت"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "نظام إدارة مكتب العقارات مع واجهة ويب وGUI"
APP_AUTHOR = "شركة المبرمج المصري"
APP_COPYRIGHT = "جميع الحقوق محفوظة © 2025"
APP_WEBSITE = "https://www.facebook.com/almbarmg"
APP_CONTACT = "0201032540807"

# معلومات البناء
BUILD_DATE = "2025-01-01"
BUILD_TYPE = "Release"
PYTHON_VERSION = "3.8+"

# معلومات الملف التنفيذي
EXE_NAME = "مدير_خادم_مكتب_عصام_الفت.exe"
EXE_DESCRIPTION = "مدير خادم مكتب عصام الفت لإدارة الأملاك"

# إعدادات قاعدة البيانات
DEFAULT_USERNAME = "admin"
DEFAULT_PASSWORD = "admin123"
DATABASE_NAME = "realestate.db"

# معلومات الشبكة
DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 5000
NETWORK_HOST = "0.0.0.0"

def get_version_info():
    """إرجاع معلومات الإصدار كقاموس"""
    return {
        'name': APP_NAME,
        'version': APP_VERSION,
        'description': APP_DESCRIPTION,
        'author': APP_AUTHOR,
        'copyright': APP_COPYRIGHT,
        'website': APP_WEBSITE,
        'contact': APP_CONTACT,
        'build_date': BUILD_DATE,
        'build_type': BUILD_TYPE,
        'python_version': PYTHON_VERSION
    }

def get_app_info():
    """إرجاع معلومات التطبيق للعرض"""
    return f"""
{APP_NAME} v{APP_VERSION}

{APP_DESCRIPTION}

تم التطوير والبرمجة بواسطة: {APP_AUTHOR}
الموقع: {APP_WEBSITE}
واتساب: {APP_CONTACT}

{APP_COPYRIGHT}

تاريخ البناء: {BUILD_DATE}
نوع البناء: {BUILD_TYPE}
متطلبات Python: {PYTHON_VERSION}
"""

def print_version_info():
    """طباعة معلومات الإصدار"""
    print(get_app_info())

if __name__ == "__main__":
    print_version_info()
